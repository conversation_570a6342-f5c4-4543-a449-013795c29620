"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserStorySchema = void 0;
const genkit_1 = require("genkit");
// Schema for valid user stories
const AcceptedStory = genkit_1.z.object({
    isRejected: genkit_1.z.boolean().refine(val => val === false, {
        message: "isRejected must be false for AcceptedStory"
    }),
    title: genkit_1.z.string().describe("A concise, descriptive title for the user story"),
    role: genkit_1.z.string().describe("The specific user role or persona who will benefit from this feature"),
    goal: genkit_1.z.string().describe("What the user wants to accomplish"),
    benefit: genkit_1.z.string().describe("The value or benefit the user will receive from this feature"),
    formattedStory: genkit_1.z.string().describe("Complete user story in standard format: 'As a [role], I want to [goal], so that [benefit]'"),
    // Evaluation fields
    feasibility: genkit_1.z.number().min(1).max(5).describe("Technical feasibility rating from 1 (very difficult) to 5 (very feasible)"),
    complexity: genkit_1.z.number().min(1).max(10).describe("Implementation complexity from 1 (very simple) to 10 (extremely complex)"),
    priority: genkit_1.z.enum(["Critical", "High", "Medium", "Low"]).describe("Business priority level for this feature"),
    featureCategory: genkit_1.z.string().describe("Category or type of feature (e.g., 'Authentication', 'Reporting', 'User Management')"),
    issueAnalysis: genkit_1.z.string().optional().describe("Analysis of relationships to similar (but non-duplicate) existing issues"),
});
// Schema for rejected/duplicate stories
const RejectedStory = genkit_1.z.object({
    isRejected: genkit_1.z.boolean().refine(val => val === true, {
        message: "isRejected must be true for RejectedStory"
    }),
    title: genkit_1.z.string().optional().describe("The title of the rejected story or duplicate issue"),
    rejectionReason: genkit_1.z.string().describe("Reason why this story was rejected"),
    duplicateIssueNumber: genkit_1.z.number().describe("Issue number of the duplicate"),
    duplicateIssueTitle: genkit_1.z.string().describe("Title of the duplicate issue"),
});
// Alternative approach without discriminated union
exports.UserStorySchema = genkit_1.z.union([
    AcceptedStory,
    RejectedStory,
]);
//# sourceMappingURL=story-schema.js.map