{"version": 3, "file": "story-schema.js", "sourceRoot": "", "sources": ["../../../../src/lib/ai/schemas/story-schema.ts"], "names": [], "mappings": ";;;AAAA,mCAA2B;AAE3B,gCAAgC;AAChC,MAAM,aAAa,GAAG,UAAC,CAAC,MAAM,CAAC;IAC7B,UAAU,EAAE,UAAC,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,KAAK,EAAE;QACnD,OAAO,EAAE,4CAA4C;KACtD,CAAC;IACF,KAAK,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,iDAAiD,CAAC;IAC7E,IAAI,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sEAAsE,CAAC;IACjG,IAAI,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mCAAmC,CAAC;IAC9D,OAAO,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8DAA8D,CAAC;IAC5F,cAAc,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4FAA4F,CAAC;IACjI,oBAAoB;IACpB,WAAW,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,2EAA2E,CAAC;IAC3H,UAAU,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,0EAA0E,CAAC;IAC1H,QAAQ,EAAE,UAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,0CAA0C,CAAC;IAC5G,eAAe,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sFAAsF,CAAC;IAC5H,aAAa,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0EAA0E,CAAC;CAC1H,CAAC,CAAC;AAEH,wCAAwC;AACxC,MAAM,aAAa,GAAG,UAAC,CAAC,MAAM,CAAC;IAC7B,UAAU,EAAE,UAAC,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,IAAI,EAAE;QAClD,OAAO,EAAE,2CAA2C;KACrD,CAAC;IACF,KAAK,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,oDAAoD,CAAC;IAC3F,eAAe,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,oCAAoC,CAAC;IAC1E,oBAAoB,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,+BAA+B,CAAC;IAC1E,mBAAmB,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;CACzE,CAAC,CAAC;AAEH,mDAAmD;AACtC,QAAA,eAAe,GAAG,UAAC,CAAC,KAAK,CAAC;IACrC,aAAa;IACb,aAAa;CACd,CAAC,CAAC"}