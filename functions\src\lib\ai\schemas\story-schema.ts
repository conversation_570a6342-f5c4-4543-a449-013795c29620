import { z } from "genkit";

// Schema for valid user stories
const AcceptedStory = z.object({
  isRejected: z.boolean().refine(val => val === false, {
    message: "isRejected must be false for AcceptedStory"
  }),
  title: z.string().describe("A concise, descriptive title for the user story"),
  role: z.string().describe("The specific user role or persona who will benefit from this feature"),
  goal: z.string().describe("What the user wants to accomplish"),
  benefit: z.string().describe("The value or benefit the user will receive from this feature"),
  formattedStory: z.string().describe("Complete user story in standard format: 'As a [role], I want to [goal], so that [benefit]'"),
  // Evaluation fields
  feasibility: z.number().min(1).max(5).describe("Technical feasibility rating from 1 (very difficult) to 5 (very feasible)"),
  complexity: z.number().min(1).max(10).describe("Implementation complexity from 1 (very simple) to 10 (extremely complex)"),
  priority: z.enum(["Critical", "High", "Medium", "Low"]).describe("Business priority level for this feature"),
  featureCategory: z.string().describe("Category or type of feature (e.g., 'Authentication', 'Reporting', 'User Management')"),
  issueAnalysis: z.string().optional().describe("Analysis of relationships to similar (but non-duplicate) existing issues"),
});

// Schema for rejected/duplicate stories
const RejectedStory = z.object({
  isRejected: z.boolean().refine(val => val === true, {
    message: "isRejected must be true for RejectedStory"
  }),
  title: z.string().optional().describe("The title of the rejected story or duplicate issue"),
  rejectionReason: z.string().describe("Reason why this story was rejected"),
  duplicateIssueNumber: z.number().describe("Issue number of the duplicate"),
  duplicateIssueTitle: z.string().describe("Title of the duplicate issue"),
});

// Alternative approach without discriminated union
export const UserStorySchema = z.union([
  AcceptedStory,
  RejectedStory,
]);

// Type derived directly from the schema
export type UserStory = z.infer<typeof UserStorySchema>; 